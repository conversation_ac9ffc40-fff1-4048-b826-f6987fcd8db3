# %%

import os

project_root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
os.chdir(project_root_path)
markdown_path = "docs/dicom_standard/modules"
module_path = "src/pyrt_dicom/modules"
module_test_path = "tests/unit/modules"
validator_path = "src/pyrt_dicom/validators/modules"
validator_test_path = "tests/unit/validators/modules"

markdown_files = sorted([f for f in os.listdir(markdown_path) if f.endswith(".md")])
module_files = []
module_class_names = []
module_test_files = []
validator_files = []
validator_class_names = []
validator_test_files = []


for i, markdown_file in enumerate(markdown_files):
    module_file_name = markdown_file.replace(".md", "").replace("-", "_")
    module_name = module_file_name.replace("_", " ")

    module_file = f"{module_file_name}_module.py"
    module_test_file = f"test_{module_file_name}_module.py"
    validator_file = f"{module_file_name}_validator.py"
    validator_test_file = f"test_{module_file_name}_validator.py"

    if os.path.isfile(os.path.join(module_path, module_file)):
        module_files.append(module_file)

        # Get module class name from module file
        with open(os.path.join(module_path, module_file), "r") as f:
            module_class_name = f.read().split("class ")[1].split("(")[0]
        module_class_names.append(module_class_name)
    else:
        print(f"Missing module file: {module_file}")

    if os.path.isfile(os.path.join(module_test_path, module_test_file)):
        module_test_files.append(module_test_file)
    else:
        print(f"Missing module test file: {module_test_file}")

    if os.path.isfile(os.path.join(validator_path, validator_file)):
        validator_files.append(validator_file)

        # Get validator class name from validator file
        with open(os.path.join(validator_path, validator_file), "r") as f:
            validator_class_name = f.read().split("class ")[1].split("(")[0]
        validator_class_names.append(validator_class_name)
    else:
        print(f"Missing validator file: {validator_file}")

    if os.path.isfile(os.path.join(validator_test_path, validator_test_file)):
        validator_test_files.append(validator_test_file)
    else:
        print(f"Missing validator test file: {validator_test_file}")

    print(f"{i+1}: {module_class_names[-1]}")

print(
    len(markdown_files),
    len(module_files),
    len(module_class_names),
    len(validator_files),
    len(validator_class_names),
    len(module_test_files),
    len(validator_test_files),
)
print(markdown_files)
print(module_files)
print(module_class_names)
print(module_test_files)
print(validator_files)
print(validator_class_names)
print(validator_test_files)


#%%

# 1: ApprovalModule
# 2: CineModule
# 3: ClinicalTrialSeriesModule
# 4: ClinicalTrialStudyModule
# 5: ClinicalTrialSubjectModule
# 6: CommonInstanceReferenceModule
# 7: ContrastBolusModule
# 8: CTImageModule
# 9: DeviceModule
# 10: EnhancedPatientOrientationModule
# 11: FrameExtractionModule
# 12: FrameOfReferenceModule
# 13: GeneralAcquisitionModule
# 14: GeneralEquipmentModule
# 15: GeneralImageModule
# 16: GeneralReferenceModule
# 17: GeneralSeriesModule
# 18: GeneralStudyModule
# 19: ImagePixelModule
# 20: ImagePlaneModule
# 21: ModalityLutModule
# 22: MultiEnergyCTImageModule
# 23: MultiFrameModule
# 24: OverlayPlaneModule
# 25: PatientModule
# 26: PatientStudyModule
# 27: ROIContourModule
# 28: RTBeamsModule
# 29: RTBrachyApplicationSetupsModule
# 30: RTDoseModule
# 31: RTDVHModule
# 32: RTFractionSchemeModule
# 33: RTGeneralPlanModule
# 34: RTImageModule
# 35: RTPatientSetupModule
# 36: RTPrescriptionModule
# 37: RTROIObservationsModule
# 38: RTSeriesModule
# 39: RTToleranceTablesModule
# 40: SOPCommonModule
# 41: SpecimenModule
# 42: StructureSetModule
# 43: SynchronizationModule
# 44: VoiLutModule

# %%

index = 0
for i, (
    markdown_file,
    module_file,
    module_test_file,
    validator_file,
    validator_test_file,
) in enumerate(
    zip(
        markdown_files,
        module_files,
        module_test_files,
        validator_files,
        validator_test_files,
    )
):
    # if i+1 < index:
    #     continue

    module_name = markdown_file.replace(".md", "").replace("-", " ").replace("_", " ")
    print(f"\n{i + 1}: {module_name.upper()}\n")
    if True:  # i == index:
        print(f"""
You are a senior software engineer with expertise in python programming and pytest unit testing, medical physics, and DICOM standardization. You have been entrusted by executive leadership with the following tasks:
1. Review the DICOM standard definition for the "{module_name}" module in file @{os.path.join(markdown_path, markdown_file)} and think deeply about all conditional logic embedded within this module
2. Refactor the "{module_name}" module file (@{os.path.join(module_path, module_file)}) according to the module implementation guide (@docs/dicom_module_migration_guide.md) while making sure the module correctly portrays the conditional logic reviewed in step 1.
3. Refactor the "{module_name}" pytest file (@{os.path.join(module_test_path, module_test_file)}) to account for all revisions to the "{module_name}" module file in step 2.
4. Refactor the "{module_name}" validator file (@{os.path.join(validator_path, validator_file)}) to adhere to the DICOM module review checklist (@docs/dicom_module_review_checklist.md), particularly Section 2. Validator Standardization Checklist and Section 3. Semantic Validation Checklist.
5. Only after steps 1, 2, 3, and 4 are complete should you proceed to create a new "{module_name}" module validator pytest file (if one doesn't already exist) which ensures that the validator from step 4 correctly applies the DICOM standard definitions to the DICOM module class. Only include basic unit tests for now (exclude edge cases and performance tests).
Completion of these tasks will require your best effort. Do not take shortcuts for the sake of efficiency, and be absolutely sure that each of these steps is completed in full before moving on the to the next step.
""")
    i += 1
    # break


# %%

index = 0
for i, (
    markdown_file,
    module_file,
    module_test_file,
    validator_file,
    validator_test_file,
) in enumerate(
    zip(
        markdown_files,
        module_files,
        module_test_files,
        validator_files,
        validator_test_files,
    )
):
    # # Modules 1 - 14 are finished
    # if i+1 < 14:
    #     continue

    module_name = markdown_file.replace(".md", "").replace("-", " ").replace("_", " ")
    print(f"\n{i + 1}: {module_name.upper()}\n")

    prompt = f"""
Make sure the "{module_name}" module pytests in @{module_test_path}/{module_test_file} are up to date with respect to the latest changes made to the "{module_name}" dicom module in @{module_path}/{module_file}
"""
    print(prompt)
    # break

# %%

# Claude Code: 6: CommonInstanceReferenceModule
# Augment: 8: CTImageModule

index = 8

for i, (
    markdown_file,
    module_file,
    module_class_name,
    module_test_file,
    validator_file,
    validator_class_name,
    validator_test_file,
) in enumerate(
    zip(
        markdown_files,
        module_files,
        module_class_names,
        module_test_files,
        validator_files,
        validator_class_names,
        validator_test_files,
    )
):
    if i+1 < index:
        continue

    module_name = markdown_file.replace(".md", "").replace("-", " ").replace("_", " ")
    print(f"{i + 1}: {module_name.upper()}\n")

    prompt = f"""
I need to implement Phase 2 of the validation integration refactor for the {module_class_name} specifically, as described in
@docs/validation_integration_refactor_guide.md. This must be completed as a single, complete module/validator pair.

### Target Files
- DICOM Standard Definition: @{markdown_path}/{markdown_file}
- Module Class: @{module_path}/{module_file}
- Module Tests: @{module_test_path}/{module_test_file}
- Validator Class: @{validator_path}/{validator_file}
- Validator Tests: @{validator_test_path}/{validator_test_file}

### Step 2: {validator_class_name} Refactoring
1. Add granular validation methods as needed by this validator with `Dataset | BaseModule` support:
   - `validate_required_elements(data: Dataset | BaseModule) -> ValidationResult` for Type 1 DICOM attributes which should be present **AND** specified (not blank or empty)
   - `validate_conditional_requirements(data: Dataset | BaseModule) -> ValidationResult` for Type 1C and Type 2C DICOM attributes as defined in the DICOM standard definition file above. Note that Type 1C attributes, when present, need to be specified (not blank or empty), while Type 2C attributes, when present, are allowed to be blank or empty.
   - `validate_enumerated_values(data: Dataset | BaseModule) -> ValidationResult`
   - `validate_sequence_structures(data: Dataset | BaseModule) -> ValidationResult`
   - `validate_*(data: Dataset | BaseModule) -> ValidationResult` variants for any other granular validation methods required by this validator

2. Update main `validate(data: Dataset | BaseModule, config)` method to orchestrate granular methods

3. **Replace ALL** `hasattr()`, `getattr()`, `setattr()` with `'attribute' in data` and `data.attribute` patterns

4. Handle complex Type 1C/2C conditional logic for non-human organisms, alternative calendars, and responsible persons

5. Use modern `Dataset | BaseModule` type hints throughout

### Step 3: {module_class_name} Integration
1. Add public validation convenience methods as needed by this module with zero-copy:
   - `check_required_elements() -> ValidationResult`
   - `check_conditional_requirements() -> ValidationResult`
   - `check_enum_constraints() -> ValidationResult`
   - `check_sequence_requirements() -> ValidationResult`
   - `check_*() -> ValidationResult` variants for any other granular validation methods required by this module

2. Update existing `validate(config) -> ValidationResult` to use zero-copy (pass `self`)

3. Add private validation methods as needed by this module:
   - `_ensure_required_elements_valid() -> None`
   - `_ensure_conditional_requirements_valid() -> None`
   - `_ensure_enum_constraints_valid() -> None`
   - `_ensure_sequence_requirements_valid() -> None`
   - `_ensure_*(data: Dataset | BaseModule) -> None` variants for any other granular validation methods required by this module

### Steps 4-7: Testing, Validation & Completion
1. Update `{module_test_file}` with tests for all new methods including Type 1C/2C scenarios as defined in the DICOM standard definition
2. Create/update `{validator_test_file}` with comprehensive tests for both Dataset and BaseModule
3. Test complex conditional validation scenarios (non-human organisms, etc.)
4. Run integration tests: `pytest tests/unit/modules/{module_test_file} tests/unit/validators/{validator_test_file} -v`
5. Verify no regressions: `pytest tests/unit/modules/ tests/unit/validators/ -v`
6. Update docstrings and mark as ✅ **COMPLETE** in the refactor guide

**Success Criteria**: All {module_class_name}/{validator_class_name} tests pass including complex Type 1C/2C validation, zero-copy
validation works, ValidationError exceptions raised correctly, external Dataset validation still works, and no regressions in
any modules.
""".strip()
    print(prompt)
    break
print()
